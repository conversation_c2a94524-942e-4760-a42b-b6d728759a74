<?php

declare(strict_types=1);

namespace App\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\Product;
use App\Entity\ProductType;
use App\Entity\QuestionSection;
use App\Entity\Section;
use App\Repository\QuestionsRepository;
use App\Repository\QuestionSectionRepository;
use App\Repository\SectionRepository;
use App\Service\ProductService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Messenger\Attribute\AsMessageHandler;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Psr\Log\LoggerInterface;

#[AsMessageHandler]
final readonly class CreateOrUpdateSectionHandler
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SectionRepository $sectionRepository,
        private QuestionsRepository $questionsRepository,
        private QuestionSectionRepository $questionSectionRepository,
        private ProductService $productService,
        private ValidatorInterface $validator,
        private LoggerInterface $logger,
    ) {
    }

    public function __invoke(CreateOrUpdateSection $command): Section
    {
        $this->logger->info('CreateOrUpdateSectionHandler: Starting section creation', [
            'name' => $command->getName(),
            'sectionType' => $command->getSectionType()->value,
            'published' => $command->isPublished()
        ]);

        // Validate the command
        $violations = $this->validator->validate($command);
        if (count($violations) > 0) {
            $this->logger->error('CreateOrUpdateSectionHandler: Command validation failed', [
                'violations' => (string) $violations
            ]);
            throw new \InvalidArgumentException('Invalid command data: ' . (string) $violations);
        }

        try {
            $section = new Section();
            $section->setName($command->getName());
            $section->setSectionType($command->getSectionType());
            $section->setPublished($command->isPublished());



            $this->logger->info('CreateOrUpdateSectionHandler: Section entity created successfully');

            // Handle products if they exist
            if (!empty($command->getMedicalConditionSections()) || !empty($command->getProductSections())) {
                $this->logger->info('CreateOrUpdateSectionHandler: Adding products to section');
                $this->addProductsToSection($section, $command);
            }

            if (!empty($command->getQuestionSections())) {
                $this->logger->info('CreateOrUpdateSectionHandler: Adding question sections to section');
                $this->addQuestionSectionsToSection($section, $command);
            }

            $this->logger->info('CreateOrUpdateSectionHandler: Saving section to database');
            $this->sectionRepository->add($section, true);

            $this->logger->info('CreateOrUpdateSectionHandler: Section created successfully', [
                'sectionId' => $section->getId()
            ]);

            return $section;
        } catch (\Exception $e) {
            $this->logger->error('CreateOrUpdateSectionHandler: Error creating section', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    private function addProductsToSection(Section $section, CreateOrUpdateSection $command): void
    {
        $productsData = array_merge(
            $command->getMedicalConditionSections(),
            $command->getProductSections()
        );

        foreach ($productsData as $productData) {
            $productCode = $productData['medicalConditionId'] ?? $productData['productId'] ?? null;
            if (!is_string($productCode)) {
                $this->logger->warning('CreateOrUpdateSectionHandler: Skipping product with invalid code', [
                    'productData' => $productData
                ]);
                continue;
            }

            if (!isset($productData['name']) || !is_string($productData['name'])) {
                $this->logger->warning('CreateOrUpdateSectionHandler: Skipping product with missing name', [
                    'productCode' => $productCode
                ]);
                continue;
            }

            $productType = array_key_exists('medicalConditionId', $productData)
                ? ProductType::Consult
                : ProductType::Medication;

            $productDataForService = [
                'code' => $productCode,
                'name' => $productData['name'],
                'type' => $productType,
            ];

            $product = $this->productService->createOrUpdateProduct($productDataForService, 'en');
            $section->addProduct($product);
        }
    }

    private function addQuestionSectionsToSection(Section $section, CreateOrUpdateSection $command): void
    {
        foreach ($command->getQuestionSections() as $questionSectionData) {
            // Handle existing QuestionSection (when 'id' is provided)
            if (isset($questionSectionData['id'])) {
                $questionSection = $this->questionSectionRepository->find($questionSectionData['id']);
                if (!$questionSection) {
                    $this->logger->warning('CreateOrUpdateSectionHandler: QuestionSection not found', [
                        'questionSectionId' => $questionSectionData['id']
                    ]);
                    continue; // Skip if QuestionSection doesn't exist
                }

                $questionSection->setSort($questionSectionData['sort'] ?? $questionSection->getSort());
                $section->addQuestionSection($questionSection);
                continue;
            }

            // Handle new QuestionSection (when 'question' is provided)
            if (isset($questionSectionData['question'])) {
                $questionId = $questionSectionData['question'];
                $sort = $questionSectionData['sort'] ?? 0;

                $question = $this->questionsRepository->find($questionId);
                if (!$question) {
                    $this->logger->warning('CreateOrUpdateSectionHandler: Question not found', [
                        'questionId' => $questionId
                    ]);
                    continue; // Skip if question doesn't exist
                }

                $questionSection = new QuestionSection();
                $questionSection->setQuestion($question);
                $questionSection->setSort($sort);
                $questionSection->setSection($section);

                $section->addQuestionSection($questionSection);
            }
        }
    }
}
