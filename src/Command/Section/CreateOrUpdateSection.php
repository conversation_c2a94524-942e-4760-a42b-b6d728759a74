<?php

declare(strict_types=1);

namespace App\Command\Section;

use App\Entity\SectionType;
use <PERSON>ymfony\Component\Validator\Constraints as Assert;

final readonly class CreateOrUpdateSection
{
    /**
     * @param string $name The name of the section
     * @param SectionType $sectionType The type of section (generalHealth, medicalCondition, product, other)
     * @param bool $published Whether the section is published and visible to users
     * @param bool|null $status @deprecated Use $published instead. Kept for backward compatibility.
     * @param array $medicalConditionSections Array of medical condition data
     * @param array $productSections Array of product/medication data
     * @param array $questionSections Array of question section data
     * @param mixed|null $generalSections General sections data (legacy)
     * @param int|null $deleted Soft delete flag (0 = not deleted, 1 = deleted)
     * @param int|null $id Section ID (for updates)
     * @param string|null $createdAt Creation timestamp
     * @param string|null $updatedAt Last update timestamp
     */
    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(max: 200)]
        private string $name,

        #[Assert\NotNull]
        private SectionType $sectionType,

        #[Assert\NotNull]
        private bool $published,

        /**
         * @deprecated This property is deprecated since version 2.5. Use the `published` property instead.
         * This field is kept for backward compatibility with existing API clients.
         * When both `status` and `published` are provided, `published` takes precedence.
         */
        private ?bool $status = null,

        #[Assert\Type('array')]
        private array $medicalConditionSections = [],

        #[Assert\Type('array')]
        private array $productSections = [],

        #[Assert\Type('array')]
        private array $questionSections = [],

        private mixed $generalSections = null,

        private ?int $deleted = null,

        private ?int $id = null,

        private ?string $createdAt = null,

        private ?string $updatedAt = null,
    )
    {
    }

    /**
     * @return string
     */
    public function getName(): string
    {
        return $this->name;
    }

    /**
     * @return SectionType
     */
    public function getSectionType(): SectionType
    {
        return $this->sectionType;
    }

    /**
     * @return bool
     */
    public function isPublished(): bool
    {
        return $this->published;
    }

    /**
     * @return bool|null
     * @deprecated This method is deprecated since version 2.5. Use isPublished() instead.
     */
    public function getStatus(): ?bool
    {
        return $this->status;
    }

    /**
     * @return array
     */
    public function getMedicalConditionSections(): array
    {
        return $this->medicalConditionSections;
    }

    /**
     * @return array
     */
    public function getProductSections(): array
    {
        return $this->productSections;
    }

    /**
     * @return array
     */
    public function getQuestionSections(): array
    {
        return $this->questionSections;
    }

    /**
     * @return mixed
     */
    public function getGeneralSections(): mixed
    {
        return $this->generalSections;
    }

    /**
     * @return int|null
     */
    public function isDeleted(): ?int
    {
        return $this->deleted;
    }

    /**
     * @return int|null
     */
    public function getId(): ?int
    {
        return $this->id;
    }

    /**
     * @return string|null
     */
    public function getCreatedAt(): ?string
    {
        return $this->createdAt;
    }

    /**
     * @return string|null
     */
    public function getUpdatedAt(): ?string
    {
        return $this->updatedAt;
    }
}
