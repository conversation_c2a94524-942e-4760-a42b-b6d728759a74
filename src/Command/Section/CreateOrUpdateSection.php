<?php

declare(strict_types=1);

namespace App\Command\Section;

use App\Entity\SectionType;
use Symfony\Component\Validator\Constraints as Assert;

final readonly class CreateOrUpdateSection
{

    public function __construct(
        #[Assert\NotBlank]
        #[Assert\Length(max: 200)]
        private string $name,

        #[Assert\NotNull]
        private SectionType $sectionType,

        #[Assert\NotNull]
        private bool $published,



        #[Assert\Type('array')]
        private array $medicalConditionSections = [],

        #[Assert\Type('array')]
        private array $productSections = [],

        #[Assert\Type('array')]
        private array $questionSections = [],

        private ?int $deleted = null,

        private ?int $id = null
    ) {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getSectionType(): SectionType
    {
        return $this->sectionType;
    }

    public function isPublished(): bool
    {
        return $this->published;
    }



    public function getMedicalConditionSections(): array
    {
        return $this->medicalConditionSections;
    }

    public function getProductSections(): array
    {
        return $this->productSections;
    }

    public function getQuestionSections(): array
    {
        return $this->questionSections;
    }

    public function isDeleted(): ?int
    {
        return $this->deleted;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

}
