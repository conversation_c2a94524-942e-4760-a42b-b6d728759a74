<?php

declare(strict_types=1);

namespace App\Utils;

use App\Entity\Section;
use App\Entity\SectionType;

/**
 * Helper utilities for working with sections.
 *
 * I created this because I kept writing the same logic in different places.
 * It's not perfect but it saves time and reduces duplication.
 */
class SectionHelper
{
    /**
     * Check if a section name is valid for the given type.
     *
     * Some section types have specific naming requirements that aren't
     * covered by the basic validation. This helps catch issues early.
     */
    public static function isValidSectionName(string $name, SectionType $type): bool
    {
        // Basic checks first
        if (empty(trim($name))) {
            return false;
        }

        if (strlen($name) > 200) {
            return false;
        }

        // Type-specific validation
        switch ($type) {
            case SectionType::MedicalCondition:
                // Medical condition sections should probably mention the condition
                // This is more of a guideline than a hard rule
                return !str_contains(strtolower($name), 'test') ||
                       str_contains(strtolower($name), 'condition') ||
                       str_contains(strtolower($name), 'medical');

            case SectionType::Product:
                // Product sections often mention medications or products
                return str_contains(strtolower($name), 'product') ||
                       str_contains(strtolower($name), 'medication') ||
                       str_contains(strtolower($name), 'drug') ||
                       str_contains(strtolower($name), 'treatment');

            case SectionType::GeneralHealth:
            case SectionType::Other:
            default:
                // These are pretty flexible
                return true;
        }
    }

    /**
     * Generate a suggested name based on the section type and content.
     *
     * This is useful when creating sections programmatically or
     * when users don't provide a good name.
     */
    public static function suggestSectionName(SectionType $type, array $context = []): string
    {
        $baseName = match ($type) {
            SectionType::GeneralHealth => 'General Health Questions',
            SectionType::MedicalCondition => 'Medical Condition Assessment',
            SectionType::Product => 'Product Information',
            SectionType::Other => 'Additional Questions',
        };

        // Try to make it more specific based on context
        if (!empty($context['medicalConditions'])) {
            $conditions = $context['medicalConditions'];
            if (count($conditions) === 1) {
                $conditionName = $conditions[0]['name'] ?? 'Condition';
                return "{$conditionName} Assessment";
            } elseif (count($conditions) > 1) {
                return "Multiple Conditions Assessment";
            }
        }

        if (!empty($context['products'])) {
            $products = $context['products'];
            if (count($products) === 1) {
                $productName = $products[0]['name'] ?? 'Product';
                return "{$productName} Information";
            } elseif (count($products) > 1) {
                return "Multiple Products Information";
            }
        }

        return $baseName;
    }

    /**
     * Check if a section has any associated content.
     *
     * Sometimes we end up with empty sections and this helps identify them.
     */
    public static function hasContent(Section $section): bool
    {
        // Check if it has questions
        if ($section->getQuestionSections()->count() > 0) {
            return true;
        }

        // Check if it has products
        if ($section->getProducts()->count() > 0) {
            return true;
        }

        // If it's published, we consider it to have content
        // (even if it's just a placeholder for now)
        if ($section->isPublished()) {
            return true;
        }

        return false;
    }

    /**
     * Get a human-readable description of what's in the section.
     *
     * Useful for admin interfaces and debugging.
     */
    public static function getContentSummary(Section $section): string
    {
        $parts = [];

        $questionCount = $section->getQuestionSections()->count();
        if ($questionCount > 0) {
            $parts[] = "{$questionCount} question" . ($questionCount === 1 ? '' : 's');
        }

        $productCount = $section->getProducts()->count();
        if ($productCount > 0) {
            $parts[] = "{$productCount} product" . ($productCount === 1 ? '' : 's');
        }

        if (empty($parts)) {
            return 'No content';
        }

        $summary = implode(', ', $parts);

        if (!$section->isPublished()) {
            $summary .= ' (unpublished)';
        }

        return $summary;
    }

    /**
     * Clean up section name for display.
     *
     * Sometimes names come in with weird formatting or extra spaces.
     */
    public static function cleanSectionName(string $name): string
    {
        // Remove extra whitespace
        $cleaned = trim($name);
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);

        // Make it title case - capitalize each word
        if (!empty($cleaned)) {
            $cleaned = ucwords(strtolower($cleaned));
        }

        return $cleaned;
    }

    /**
     * Check if two sections are similar (might be duplicates).
     *
     * This is a simple heuristic - not perfect but catches obvious cases.
     */
    public static function areSimilar(Section $section1, Section $section2): bool
    {
        // Same type is a good start
        if ($section1->getSectionType() !== $section2->getSectionType()) {
            return false;
        }

        // Similar names?
        $name1 = strtolower(trim($section1->getName() ?? ''));
        $name2 = strtolower(trim($section2->getName() ?? ''));

        if ($name1 === $name2) {
            return true;
        }

        // Check if one name contains the other (might be variations)
        if (strlen($name1) > 5 && strlen($name2) > 5) {
            if (str_contains($name1, $name2) || str_contains($name2, $name1)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get sections that might be related to this one.
     *
     * This is used in the admin interface to help organize sections.
     *
     * @param Section[] $allSections
     * @return Section[]
     */
    public static function findRelatedSections(Section $targetSection, array $allSections): array
    {
        $related = [];

        foreach ($allSections as $section) {
            // Skip the target section itself
            if ($section->getId() === $targetSection->getId()) {
                continue;
            }

            // Same type is related
            if ($section->getSectionType() === $targetSection->getSectionType()) {
                $related[] = $section;
                continue;
            }

            // Check for shared products
            $targetProducts = $targetSection->getProducts()->toArray();
            $sectionProducts = $section->getProducts()->toArray();

            foreach ($targetProducts as $targetProduct) {
                foreach ($sectionProducts as $sectionProduct) {
                    if ($targetProduct->getId() === $sectionProduct->getId()) {
                        $related[] = $section;
                        break 2; // Break out of both loops
                    }
                }
            }
        }

        return $related;
    }
}
