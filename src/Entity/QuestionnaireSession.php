<?php

declare(strict_types=1);

namespace App\Entity;

use App\Enum\Gender;
use App\Repository\QuestionnaireSessionsRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Ged<PERSON>\Timestampable\Traits\TimestampableEntity;
use Ramsey\Uuid\Doctrine\UuidGenerator;
use Ramsey\Uuid\Uuid;
use Ramsey\Uuid\UuidInterface;

#[ORM\Entity(repositoryClass: QuestionnaireSessionsRepository::class)]
#[ORM\Table('questionnaire_session')]
class QuestionnaireSession
{
    use TimestampableEntity;

    #[ORM\GeneratedValue(strategy: 'CUSTOM')]
    #[ORM\CustomIdGenerator(class: UuidGenerator::class)]
    #[ORM\Column(type: 'uuid', unique: true)]
    private UuidInterface $uuid;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private ?int $id = null;

    // Why isn't this a relation to see App/Entity/Languages
    #[ORM\Column(type: 'integer')]
    private ?int $languageId = null;

    #[ORM\ManyToOne(targetEntity: Language::class)]
    private Language $language;

    /**
     * @var Collection<array-key, QuestionnaireResponse>
     */
    #[ORM\OneToMany(targetEntity: QuestionnaireResponse::class, mappedBy: 'questionnaireSession', cascade: ['persist', 'remove'])]
    private Collection $questionnaireResponses;

    #[ORM\Column(type: 'string', length: 1, nullable: false, enumType: Gender::class)]
    private Gender $genderAtBirth;

    #[ORM\Column(options: ['default' => false])]
    private bool $finished = false;

    /**
     * @var Collection<array-key, Product>
     */
    #[ORM\ManyToMany(targetEntity: Product::class)]
    private Collection $products;

    public function __construct(Gender $genderAtBirth)
    {
        $this->uuid = Uuid::uuid6();
        $this->questionnaireResponses = new ArrayCollection();
        $this->genderAtBirth = $genderAtBirth;
        $this->products = new ArrayCollection();
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getLanguageId(): ?int
    {
        return $this->languageId;
    }

    public function setLanguageId(int $languageId): self
    {
        $this->languageId = $languageId;

        return $this;
    }

    public function getLocaleCode(): string
    {
        return $this->language->getLocaleCode();
    }

    /**
     * @return Collection<int, QuestionnaireResponse>
     */
    public function getQuestionnaireResponses(): Collection
    {
        return $this->questionnaireResponses;
    }

    public function addQuestionnaireResponse(QuestionnaireResponse $questionnaireResponse): self
    {
        if (!$this->questionnaireResponses->contains($questionnaireResponse)) {
            $this->questionnaireResponses[] = $questionnaireResponse;
            $questionnaireResponse->setQuestionnaireSession($this);
        }

        return $this;
    }

    public function removeQuestionnaireResponse(QuestionnaireResponse $questionnaireResponse): self
    {
        if ($this->questionnaireResponses->removeElement($questionnaireResponse)) {
            // set the owning side to null (unless already changed)
            if ($questionnaireResponse->getQuestionnaireSession() === $this) {
                $questionnaireResponse->setQuestionnaireSession(null);
            }
        }

        return $this;
    }

    public function getUuid(): UuidInterface
    {
        return $this->uuid;
    }



    public function isFinished(): bool
    {
        return $this->finished;
    }

    public function setFinished(bool $finished): self
    {
        $this->finished = $finished;

        return $this;
    }

    public function getLanguage(): Language
    {
        return $this->language;
    }

    public function setLanguage(Language $language): void
    {
        $this->language = $language;
    }

    public function getGenderAtBirth(): Gender
    {
        return $this->genderAtBirth;
    }

    public function setGenderAtBirth(Gender $genderAtBirth): void
    {
        $this->genderAtBirth = $genderAtBirth;
    }

    /**
     * @return Collection<array-key, Product>
     */
    public function getProducts(?ProductType $productType = null): Collection
    {
        if ($productType instanceof ProductType) {
            return $this->products->filter(static fn (Product $product) => $product->getProductType() === $productType);
        }

        return $this->products;
    }

    public function addProduct(Product $product): void
    {
        if ($this->products->contains($product)) {
            return;
        }

        $this->products->add($product);
    }

    public function removeProduct(Product $product): void
    {
        if (!$this->products->contains($product)) {
            return;
        }

        $this->products->removeElement($product);
    }
}
