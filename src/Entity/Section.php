<?php

declare(strict_types=1);

namespace App\Entity;

use App\Repository\SectionRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Gedmo\Timestampable\Traits\TimestampableEntity;

#[ORM\Entity(repositoryClass: SectionRepository::class)]
class Section
{
    use TimestampableEntity;

    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'string', length: 200)]
    private ?string $name;

    #[ORM\Column(type: 'string', enumType: SectionType::class)]
    private SectionType $sectionType;



    #[ORM\Column(type: 'boolean')]
    private bool $published;

    #[ORM\OneToMany(targetEntity: QuestionSection::class, mappedBy: 'section', cascade: ['persist'], orphanRemoval: true)]
    #[ORM\JoinColumn(name: 'question_sections', nullable: false)]
    private Collection $questionSections;

    #[ORM\Column(type: 'boolean', nullable: true, options: ['default' => false])]
    private ?bool $deleted;

    /**
     * @var Collection<array-key, Product>
     */
    #[ORM\ManyToMany(targetEntity: Product::class, cascade: ['persist'], fetch: 'EAGER')]
    #[ORM\JoinTable(name: 'section_product')]
    private Collection $products;

    public function __construct()
    {
        $this->questionSections = new ArrayCollection();
        $this->deleted = false;
        $this->products = new ArrayCollection();
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId($id): void
    {
        $this->id = $id;
    }

    public function getName(): ?string
    {
        return $this->name;
    }

    public function setName(string $name): self
    {
        $this->name = $name;

        return $this;
    }

    public function getSectionType(): SectionType
    {
        return $this->sectionType;
    }

    public function setSectionType(SectionType $sectionType): void
    {
        $this->sectionType = $sectionType;
    }



    public function isPublished(): bool
    {
        return $this->published;
    }

    public function setPublished(bool $published): void
    {
        $this->published = $published;
    }

    /**
     * Filters questions and question sections in this section that are deleted.
     *
     * @return Collection<int, QuestionSection>
     */
    public function getQuestionSections(): Collection
    {
        $filtered = $this->questionSections
            ->filter(fn (QuestionSection $questionSection) => !$questionSection->getQuestion()->isDeleted())
            ->filter(fn (QuestionSection $questionSection) => !$questionSection->isDeleted());

        // Recreate the ArrayCollection starting from index 0, so it won't be serialized as an object
        return new ArrayCollection($filtered->getValues());
    }

    /**
     * Filters deleted questions and returns deleted question sections.
     *
     * @return Collection<int, QuestionSection>
     */
    public function getDeletedQuestionSections(): Collection
    {
        $filtered = $this->questionSections
            ->filter(fn (QuestionSection $questionSection) => !$questionSection->getQuestion()->isDeleted())
            ->filter(fn (QuestionSection $questionSection) => $questionSection->isDeleted());

        // Recreate the ArrayCollection starting from index 0, so it won't be serialized as an object
        return new ArrayCollection($filtered->getValues());
    }

    public function addQuestionSection(QuestionSection $questionSection): void
    {
        if ($this->questionSections->contains($questionSection)) {
            return;
        }

        $this->questionSections->add($questionSection);
        $questionSection->setSection($this);
    }

    public function isDeleted(): ?bool
    {
        return $this->deleted;
    }

    public function setDeleted(bool $deleted): void
    {
        $this->deleted = $deleted;
    }

    /**
     * @return Collection<array-key, Product>
     */
    public function getProducts(): Collection
    {
        return $this->products;
    }

    public function addProduct(Product $product): void
    {
        if ($this->products->contains($product)) {
            return;
        }

        $this->products->add($product);
    }

    public function removeProduct(Product $product): void
    {
        $this->products->removeElement($product);
    }

    /**
     * @return Collection<array-key, Product>
     */
    public function getProductsByType(ProductType $productType): Collection
    {
        return $this->products->filter(
            static fn (Product $product): bool => $product->getProductType() === $productType
        );
    }
}
