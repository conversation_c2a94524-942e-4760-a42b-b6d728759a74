<?php

declare(strict_types=1);

namespace App\Tests\Integration\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Integration test to verify that the CreateOrUpdateSection command
 * works correctly with validation and contains all expected data.
 * 
 * This test simulates what happens when the CommandController receives
 * a request and deserializes it into our command object.
 */
class CreateSectionIntegrationTest extends TestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        // Set up the Symfony validator to test our command validation
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();
    }

    /**
     * Test that demonstrates the complete flow from request data to command creation.
     * This simulates what the OpenAPI bundle does when it deserializes the request.
     */
    public function testCreateSectionCommandWithCompleteData(): void
    {
        // This simulates the JSON request body that would come from the frontend
        $requestData = [
            'name' => 'Test Medical Section',
            'sectionType' => SectionType::MedicalCondition,
            'published' => true,
            'status' => true, // deprecated but still supported
            'medicalConditionSections' => [
                [
                    'medicalConditionId' => 'MC001',
                    'name' => 'Diabetes Type 2 Consultation'
                ]
            ],
            'productSections' => [
                [
                    'productId' => 'PROD123',
                    'name' => 'Metformin 500mg'
                ]
            ],
            'questionSections' => [
                [
                    'question' => 1,
                    'sort' => 0
                ],
                [
                    'question' => 2,
                    'sort' => 1
                ]
            ]
        ];

        // Create the command object (this is what the OpenAPI bundle does)
        $command = new CreateOrUpdateSection(
            name: $requestData['name'],
            sectionType: $requestData['sectionType'],
            published: $requestData['published'],
            status: $requestData['status'],
            medicalConditionSections: $requestData['medicalConditionSections'],
            productSections: $requestData['productSections'],
            questionSections: $requestData['questionSections']
        );

        // Validate the command (this happens in the handler)
        $violations = $this->validator->validate($command);
        
        // Assert that validation passes
        $this->assertCount(0, $violations, 'Command should be valid');

        // Verify all data is correctly stored in the command
        $this->assertEquals('Test Medical Section', $command->getName());
        $this->assertEquals(SectionType::MedicalCondition, $command->getSectionType());
        $this->assertTrue($command->isPublished());
        $this->assertTrue($command->getStatus());

        // Verify medical condition sections
        $medicalConditions = $command->getMedicalConditionSections();
        $this->assertCount(1, $medicalConditions);
        $this->assertEquals('MC001', $medicalConditions[0]['medicalConditionId']);
        $this->assertEquals('Diabetes Type 2 Consultation', $medicalConditions[0]['name']);

        // Verify product sections
        $products = $command->getProductSections();
        $this->assertCount(1, $products);
        $this->assertEquals('PROD123', $products[0]['productId']);
        $this->assertEquals('Metformin 500mg', $products[0]['name']);

        // Verify question sections
        $questions = $command->getQuestionSections();
        $this->assertCount(2, $questions);
        $this->assertEquals(1, $questions[0]['question']);
        $this->assertEquals(0, $questions[0]['sort']);
        $this->assertEquals(2, $questions[1]['question']);
        $this->assertEquals(1, $questions[1]['sort']);
    }

    /**
     * Test validation with invalid data to ensure our command properly validates.
     */
    public function testCreateSectionCommandValidation(): void
    {
        // Test with invalid data (empty name should fail validation)
        $command = new CreateOrUpdateSection(
            name: '', // This should fail validation
            sectionType: SectionType::GeneralHealth,
            published: true
        );

        $violations = $this->validator->validate($command);
        
        // Should have validation errors for empty name
        $this->assertGreaterThan(0, $violations->count(), 'Should have validation errors for empty name');
        
        // Check that the error is about the name field
        $nameViolations = [];
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'name') {
                $nameViolations[] = $violation;
            }
        }
        
        $this->assertGreaterThan(0, count($nameViolations), 'Should have validation error for name field');
    }

    /**
     * Test with minimal valid data to ensure the command works with required fields only.
     */
    public function testCreateSectionCommandWithMinimalData(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Simple Section',
            sectionType: SectionType::Other,
            published: false
        );

        $violations = $this->validator->validate($command);
        
        // Should pass validation with minimal required data
        $this->assertCount(0, $violations, 'Command with minimal data should be valid');
        
        // Verify the data
        $this->assertEquals('Simple Section', $command->getName());
        $this->assertEquals(SectionType::Other, $command->getSectionType());
        $this->assertFalse($command->isPublished());
        
        // Optional fields should be empty/null
        $this->assertEmpty($command->getMedicalConditionSections());
        $this->assertEmpty($command->getProductSections());
        $this->assertEmpty($command->getQuestionSections());
        $this->assertNull($command->getStatus());
    }
}
