<?php

declare(strict_types=1);

namespace App\Tests\Unit\Command\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Test validation rules for the CreateOrUpdateSection command.
 * 
 * These tests make sure we catch invalid data before it gets to the handler.
 * I learned the hard way that validation at the command level saves a lot of debugging time later!
 */
class CreateSectionCommandValidationTest extends TestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        // Set up Symfony validator - this took me a while to figure out initially
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();
    }

    public function testValidSectionCommand(): void
    {
        $cmd = new CreateOrUpdateSection(
            name: 'Diabetes Questionnaire',
            sectionType: SectionType::MedicalCondition,
            published: true
        );

        $violations = $this->validator->validate($cmd);
        
        $this->assertCount(0, $violations, 'Valid command should pass validation');
    }

    public function testEmptyNameShouldFail(): void
    {
        $cmd = new CreateOrUpdateSection(
            name: '', // This should trigger validation error
            sectionType: SectionType::GeneralHealth,
            published: false
        );

        $violations = $this->validator->validate($cmd);
        
        $this->assertGreaterThan(0, $violations->count());
        
        // Check that we get the right error message
        $nameErrors = [];
        foreach ($violations as $violation) {
            if ($violation->getPropertyPath() === 'name') {
                $nameErrors[] = $violation->getMessage();
            }
        }
        
        $this->assertNotEmpty($nameErrors, 'Should have validation error for empty name');
    }

    public function testNameTooLongShouldFail(): void
    {
        // Create a really long name that exceeds the 200 char limit
        $longName = str_repeat('A', 201);
        
        $cmd = new CreateOrUpdateSection(
            name: $longName,
            sectionType: SectionType::Product,
            published: true
        );

        $violations = $this->validator->validate($cmd);
        $this->assertGreaterThan(0, $violations->count());
    }

    /**
     * Test with medical condition data - this is pretty common in our system
     */
    public function testWithMedicalConditionData(): void
    {
        $medicalConditions = [
            [
                'medicalConditionId' => 'DIAB_T2',
                'name' => 'Type 2 Diabetes'
            ],
            [
                'medicalConditionId' => 'HYPER',
                'name' => 'Hypertension'
            ]
        ];

        $cmd = new CreateOrUpdateSection(
            name: 'Chronic Conditions Section',
            sectionType: SectionType::MedicalCondition,
            published: true,
            medicalConditionSections: $medicalConditions
        );

        $violations = $this->validator->validate($cmd);
        $this->assertCount(0, $violations);
        
        // Make sure the data is stored correctly
        $this->assertCount(2, $cmd->getMedicalConditionSections());
        $this->assertEquals('DIAB_T2', $cmd->getMedicalConditionSections()[0]['medicalConditionId']);
    }

    /**
     * Test product sections - medications and such
     */
    public function testWithProductData(): void
    {
        $products = [
            [
                'productId' => 'MET500',
                'name' => 'Metformin 500mg'
            ]
        ];

        $cmd = new CreateOrUpdateSection(
            name: 'Diabetes Medications',
            sectionType: SectionType::Product,
            published: true,
            productSections: $products
        );

        $violations = $this->validator->validate($cmd);
        $this->assertCount(0, $violations);
        
        $this->assertCount(1, $cmd->getProductSections());
        $this->assertEquals('MET500', $cmd->getProductSections()[0]['productId']);
    }

    /**
     * Test question sections - these link questions to the section
     */
    public function testWithQuestionSections(): void
    {
        $questions = [
            ['question' => 1, 'sort' => 0],
            ['question' => 2, 'sort' => 1],
            ['question' => 3, 'sort' => 2]
        ];

        $cmd = new CreateOrUpdateSection(
            name: 'Patient History Questions',
            sectionType: SectionType::GeneralHealth,
            published: true,
            questionSections: $questions
        );

        $violations = $this->validator->validate($cmd);
        $this->assertCount(0, $violations);
        
        $this->assertCount(3, $cmd->getQuestionSections());
        // Check the sorting is preserved
        $this->assertEquals(0, $cmd->getQuestionSections()[0]['sort']);
        $this->assertEquals(1, $cmd->getQuestionSections()[1]['sort']);
    }

    /**
     * Test the deprecated status field - we still need to support this for now
     * until all the frontend clients are updated
     */
    public function testDeprecatedStatusField(): void
    {
        $cmd = new CreateOrUpdateSection(
            name: 'Legacy Section',
            sectionType: SectionType::Other,
            published: false,
            status: true // Old field that some clients might still send
        );

        $violations = $this->validator->validate($cmd);
        $this->assertCount(0, $violations, 'Should still accept deprecated status field');
        
        // Both fields should be accessible
        $this->assertFalse($cmd->isPublished());
        $this->assertTrue($cmd->getStatus());
    }

    /**
     * Test all section types to make sure they work
     */
    public function testAllSectionTypes(): void
    {
        $sectionTypes = [
            SectionType::GeneralHealth,
            SectionType::MedicalCondition,
            SectionType::Product,
            SectionType::Other
        ];

        foreach ($sectionTypes as $type) {
            $cmd = new CreateOrUpdateSection(
                name: "Test {$type->value} Section",
                sectionType: $type,
                published: true
            );

            $violations = $this->validator->validate($cmd);
            $this->assertCount(0, $violations, "Section type {$type->value} should be valid");
        }
    }

    /**
     * Test edge case - what happens with empty arrays?
     * This came up in testing and I wanted to make sure it's handled properly
     */
    public function testEmptyArraysAreOk(): void
    {
        $cmd = new CreateOrUpdateSection(
            name: 'Empty Arrays Test',
            sectionType: SectionType::GeneralHealth,
            published: true,
            medicalConditionSections: [], // Empty arrays should be fine
            productSections: [],
            questionSections: []
        );

        $violations = $this->validator->validate($cmd);
        $this->assertCount(0, $violations);
        
        // Make sure they're actually empty
        $this->assertEmpty($cmd->getMedicalConditionSections());
        $this->assertEmpty($cmd->getProductSections());
        $this->assertEmpty($cmd->getQuestionSections());
    }
}
