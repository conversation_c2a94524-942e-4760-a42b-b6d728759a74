<?php

declare(strict_types=1);

namespace App\Tests\Unit\Utils;

use App\Entity\Section;
use App\Entity\SectionType;
use App\Utils\SectionHelper;
use PHPUnit\Framework\TestCase;

/**
 * Tests for the SectionHelper utility class.
 * 
 * These are just basic tests to make sure my helper methods work correctly.
 * Nothing too fancy, just checking the main functionality.
 */
class SectionHelperTest extends TestCase
{
    public function testValidSectionNames(): void
    {
        // Test basic valid names
        $this->assertTrue(SectionHelper::isValidSectionName('General Health Questions', SectionType::GeneralHealth));
        $this->assertTrue(SectionHelper::isValidSectionName('Diabetes Assessment', SectionType::MedicalCondition));
        $this->assertTrue(SectionHelper::isValidSectionName('Medication Information', SectionType::Product));
    }

    public function testInvalidSectionNames(): void
    {
        // Empty names should be invalid
        $this->assertFalse(SectionHelper::isValidSectionName('', SectionType::GeneralHealth));
        $this->assertFalse(SectionHelper::isValidSectionName('   ', SectionType::GeneralHealth));
        
        // Too long names should be invalid
        $longName = str_repeat('A', 201);
        $this->assertFalse(SectionHelper::isValidSectionName($longName, SectionType::GeneralHealth));
    }

    public function testSuggestSectionNames(): void
    {
        // Test basic suggestions
        $this->assertEquals('General Health Questions', SectionHelper::suggestSectionName(SectionType::GeneralHealth));
        $this->assertEquals('Medical Condition Assessment', SectionHelper::suggestSectionName(SectionType::MedicalCondition));
        $this->assertEquals('Product Information', SectionHelper::suggestSectionName(SectionType::Product));
        $this->assertEquals('Additional Questions', SectionHelper::suggestSectionName(SectionType::Other));
    }

    public function testSuggestSectionNamesWithContext(): void
    {
        // Test with medical condition context
        $context = [
            'medicalConditions' => [
                ['name' => 'Diabetes Type 2']
            ]
        ];
        $suggested = SectionHelper::suggestSectionName(SectionType::MedicalCondition, $context);
        $this->assertEquals('Diabetes Type 2 Assessment', $suggested);

        // Test with multiple conditions
        $context = [
            'medicalConditions' => [
                ['name' => 'Diabetes'],
                ['name' => 'Hypertension']
            ]
        ];
        $suggested = SectionHelper::suggestSectionName(SectionType::MedicalCondition, $context);
        $this->assertEquals('Multiple Conditions Assessment', $suggested);
    }

    public function testCleanSectionName(): void
    {
        // Test cleaning up names
        $this->assertEquals('Test Section', SectionHelper::cleanSectionName('  test section  '));
        $this->assertEquals('Multiple Spaces', SectionHelper::cleanSectionName('multiple    spaces'));
        $this->assertEquals('Capitalized', SectionHelper::cleanSectionName('capitalized'));
        $this->assertEquals('', SectionHelper::cleanSectionName(''));
    }

    /**
     * Test the content summary functionality.
     * This is a bit tricky to test without real entities, so I'll keep it simple.
     */
    public function testGetContentSummaryBasic(): void
    {
        // Create a mock section for testing
        $section = $this->createMock(Section::class);
        
        // Mock empty collections
        $emptyCollection = new \Doctrine\Common\Collections\ArrayCollection();
        $section->method('getQuestionSections')->willReturn($emptyCollection);
        $section->method('getProducts')->willReturn($emptyCollection);
        $section->method('isPublished')->willReturn(false);

        $summary = SectionHelper::getContentSummary($section);
        $this->assertEquals('No content', $summary);
    }

    public function testAreSimilarSections(): void
    {
        // Create mock sections
        $section1 = $this->createMock(Section::class);
        $section1->method('getSectionType')->willReturn(SectionType::GeneralHealth);
        $section1->method('getName')->willReturn('Health Questions');

        $section2 = $this->createMock(Section::class);
        $section2->method('getSectionType')->willReturn(SectionType::GeneralHealth);
        $section2->method('getName')->willReturn('Health Questions');

        $section3 = $this->createMock(Section::class);
        $section3->method('getSectionType')->willReturn(SectionType::MedicalCondition);
        $section3->method('getName')->willReturn('Different Section');

        // Same type and name should be similar
        $this->assertTrue(SectionHelper::areSimilar($section1, $section2));
        
        // Different type should not be similar
        $this->assertFalse(SectionHelper::areSimilar($section1, $section3));
    }

    /**
     * Test edge cases and error handling
     */
    public function testEdgeCases(): void
    {
        // Test with null/empty contexts
        $suggested = SectionHelper::suggestSectionName(SectionType::GeneralHealth, []);
        $this->assertEquals('General Health Questions', $suggested);

        // Test cleaning empty string
        $this->assertEquals('', SectionHelper::cleanSectionName(''));
        
        // Test validation with edge case names
        $this->assertFalse(SectionHelper::isValidSectionName('', SectionType::GeneralHealth));
    }

    /**
     * Test the medical condition specific validation
     */
    public function testMedicalConditionValidation(): void
    {
        // Medical condition sections should ideally mention medical terms
        $this->assertTrue(SectionHelper::isValidSectionName('Medical History', SectionType::MedicalCondition));
        $this->assertTrue(SectionHelper::isValidSectionName('Diabetes Condition Assessment', SectionType::MedicalCondition));
        
        // Test sections that might not be ideal but are still valid
        // (The validation is more of a guideline than a strict rule)
        $result = SectionHelper::isValidSectionName('Random Section', SectionType::MedicalCondition);
        // This might be false depending on the implementation, which is fine
        $this->assertIsBool($result);
    }

    /**
     * Test product section validation
     */
    public function testProductSectionValidation(): void
    {
        // Product sections should mention products/medications
        $this->assertTrue(SectionHelper::isValidSectionName('Medication Information', SectionType::Product));
        $this->assertTrue(SectionHelper::isValidSectionName('Product Details', SectionType::Product));
        $this->assertTrue(SectionHelper::isValidSectionName('Drug Interactions', SectionType::Product));
        $this->assertTrue(SectionHelper::isValidSectionName('Treatment Options', SectionType::Product));
    }
}
