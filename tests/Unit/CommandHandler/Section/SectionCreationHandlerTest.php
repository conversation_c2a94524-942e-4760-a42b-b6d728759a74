<?php

declare(strict_types=1);

namespace App\Tests\Unit\CommandHandler\Section;

use App\Command\Section\CreateOrUpdateSection;
use App\Entity\SectionType;
use PHPUnit\Framework\TestCase;
use Symfony\Component\Validator\Validation;
use Symfony\Component\Validator\Validator\ValidatorInterface;

/**
 * Simple tests for section command handling.
 *
 * I'm focusing on testing the command validation and basic logic
 * without getting into the complex mocking of all dependencies.
 *
 * For full integration testing, we'd use the functional tests instead.
 */
class SectionCreationHandlerTest extends TestCase
{
    private ValidatorInterface $validator;

    protected function setUp(): void
    {
        // Just set up validation for now - keeping it simple
        $this->validator = Validation::createValidatorBuilder()
            ->enableAttributeMapping()
            ->getValidator();
    }

    /**
     * Test that we can create a valid command for basic section creation.
     * This is what gets passed to the handler.
     */
    public function testCreateBasicSectionCommand(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Basic Health Questions',
            sectionType: SectionType::GeneralHealth,
            published: true
        );

        // Validate the command
        $violations = $this->validator->validate($command);
        $this->assertCount(0, $violations, 'Basic section command should be valid');

        // Check the command data
        $this->assertEquals('Basic Health Questions', $command->getName());
        $this->assertEquals(SectionType::GeneralHealth, $command->getSectionType());
        $this->assertTrue($command->isPublished());
        $this->assertNull($command->getStatus()); // Should be null when not provided
    }

    /**
     * Test command with medical condition data
     */
    public function testCommandWithMedicalConditions(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Diabetes Assessment',
            sectionType: SectionType::MedicalCondition,
            published: true,
            medicalConditionSections: [
                ['medicalConditionId' => 'DIAB_T2', 'name' => 'Type 2 Diabetes']
            ]
        );

        $violations = $this->validator->validate($command);
        $this->assertCount(0, $violations);

        $this->assertCount(1, $command->getMedicalConditionSections());
        $this->assertEquals('DIAB_T2', $command->getMedicalConditionSections()[0]['medicalConditionId']);
    }

    /**
     * Test the deprecated status field - make sure it still validates
     */
    public function testDeprecatedStatusFieldValidation(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Legacy Section',
            sectionType: SectionType::Other,
            published: false,
            status: true // Deprecated but should still work
        );

        $violations = $this->validator->validate($command);
        $this->assertCount(0, $violations, 'Command with deprecated status should still validate');

        // Both fields should be accessible
        $this->assertFalse($command->isPublished());
        $this->assertTrue($command->getStatus());
    }

    /**
     * Test validation failure with empty name
     */
    public function testValidationFailureEmptyName(): void
    {
        $command = new CreateOrUpdateSection(
            name: '', // Invalid empty name
            sectionType: SectionType::GeneralHealth,
            published: true
        );

        $violations = $this->validator->validate($command);
        $this->assertGreaterThan(0, $violations->count(), 'Empty name should cause validation failure');
    }

    /**
     * Test with question sections data
     */
    public function testCommandWithQuestionSections(): void
    {
        $command = new CreateOrUpdateSection(
            name: 'Patient History',
            sectionType: SectionType::GeneralHealth,
            published: true,
            questionSections: [
                ['question' => 1, 'sort' => 0],
                ['question' => 2, 'sort' => 1]
            ]
        );

        $violations = $this->validator->validate($command);
        $this->assertCount(0, $violations);

        $this->assertCount(2, $command->getQuestionSections());
        $this->assertEquals(1, $command->getQuestionSections()[0]['question']);
        $this->assertEquals(0, $command->getQuestionSections()[0]['sort']);
    }

    /**
     * Test all section types work
     */
    public function testAllSectionTypesValidate(): void
    {
        $types = [
            SectionType::GeneralHealth,
            SectionType::MedicalCondition,
            SectionType::Product,
            SectionType::Other
        ];

        foreach ($types as $type) {
            $command = new CreateOrUpdateSection(
                name: "Test {$type->value} Section",
                sectionType: $type,
                published: true
            );

            $violations = $this->validator->validate($command);
            $this->assertCount(0, $violations, "Section type {$type->value} should validate");
        }
    }
}
